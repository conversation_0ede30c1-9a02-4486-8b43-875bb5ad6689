###
TranslatorManager多模版支持功能测试
测试新增的多模版数组支持和优先级选择逻辑

<AUTHOR> Team
@date 2025-07-14
###

should = require 'should'
translatorManagerLib = INCLUDE 'lib.translator/translatorManager'

describe 'TranslatorManager多模版支持测试', ->
  translatorManager = null
  
  before ->
    # 创建测试用的配置
    config = {
      openAI: { key: 'test-key', endpoint: 'test-endpoint', maxUsage: 10 }
      claude: { key: 'test-key', endpoint: 'test-endpoint', maxUsage: 10 }
      gemini: { key: 'test-key', maxUsage: 10 }
      deepseek: { key: 'test-key', endpoint: 'test-endpoint', maxUsage: 10 }
    }
    translatorManager = translatorManagerLib.createTranslatorManager(config)

  describe '多模版数组验证测试', ->
    it '应该正确验证有效的多模版数组', ->
      validTemplates = [
        {
          modelType: 'gpt'
          template: { tpl: { main: 'Test prompt for GPT' } }
          priority: 1
        }
        {
          modelType: 'claude'
          template: { tpl: { main: 'Test prompt for <PERSON>' } }
          priority: 2
        }
      ]
      
      result = translatorManager._validateTemplatesArray(validTemplates)
      should.equal result.valid, true

    it '应该拒绝无效的多模版数组', ->
      invalidTemplates = [
        {
          modelType: 'gpt'
          # 缺少template字段
          priority: 1
        }
      ]
      
      result = translatorManager._validateTemplatesArray(invalidTemplates)
      should.equal result.valid, false
      should.ok result.error.includes('missing template')

    it '应该拒绝不支持的模型类型', ->
      invalidTemplates = [
        {
          modelType: 'unsupported_model'
          template: { tpl: { main: 'Test prompt' } }
          priority: 1
        }
      ]
      
      result = translatorManager._validateTemplatesArray(invalidTemplates)
      should.equal result.valid, false
      should.ok result.error.includes('Unsupported modelType')

  describe '模版选择逻辑测试', ->
    it '应该按优先级选择最佳模版', ->
      templates = [
        {
          modelType: 'deepseek'
          template: { tpl: { main: 'DeepSeek prompt' } }
          priority: 3
        }
        {
          modelType: 'gpt'
          template: { tpl: { main: 'GPT prompt' } }
          priority: 1
        }
        {
          modelType: 'claude'
          template: { tpl: { main: 'Claude prompt' } }
          priority: 2
        }
      ]
      
      result = translatorManager._selectBestTemplate(templates)
      should.equal result.selectedTemplate.modelType, 'gpt'
      should.equal result.selectedTemplate.priority, 1

  describe '便捷方法测试', ->
    it '应该正确创建多模版数组', ->
      baseTemplate = { tpl: { main: 'Base template' } }
      modelPriorities = [
        { modelType: 'gpt', priority: 1 }
        { modelType: 'claude', priority: 2 }
      ]
      
      result = translatorManager.createMultiTemplateArray(baseTemplate, modelPriorities)
      should.equal result.length, 2
      should.equal result[0].modelType, 'gpt'
      should.equal result[0].priority, 1
      should.equal result[1].modelType, 'claude'
      should.equal result[1].priority, 2

  describe '过滤结果解析测试', ->
    it '应该正确解析PASS结果', ->
      result = translatorManager._parseFilterResult('PASS: Content is appropriate')
      should.equal result.passed, true
      should.equal result.reason, 'Content approved'

    it '应该正确解析REJECT结果', ->
      result = translatorManager._parseFilterResult('REJECT: Contains inappropriate content')
      should.equal result.passed, false
      should.ok result.reason.includes('inappropriate content')

    it '应该处理不明确的结果', ->
      result = translatorManager._parseFilterResult('Maybe this is okay')
      should.equal result.passed, false
      should.equal result.reason, 'Unclear filter result'

  describe '向后兼容性测试', ->
    it '应该支持传统的字符串prompt', ->
      # 这个测试需要mock翻译器，暂时跳过实际调用
      # 主要验证参数验证逻辑
      content = 'Test content'
      prompt = 'Translate this text'
      
      # 验证参数类型检查
      isString = typeof prompt is 'string'
      isTemplateObject = translatorManager._isTemplateObject(prompt)
      isTemplatesArray = Array.isArray(prompt)
      
      should.equal isString, true
      should.equal isTemplateObject, false
      should.equal isTemplatesArray, false

    it '应该支持传统的模版对象', ->
      templateObject = {
        tpl: { main: 'Translate {text} to {target_language}' }
        m_cfg: { m_nm: 'gpt' }
      }
      
      isTemplateObject = translatorManager._isTemplateObject(templateObject)
      should.equal isTemplateObject, true
