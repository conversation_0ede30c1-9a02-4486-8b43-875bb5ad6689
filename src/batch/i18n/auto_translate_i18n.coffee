###
Description:    自动翻译i18n表中的未翻译数据
                支持中文繁简体自动转换和韩文LLM翻译
                
Usage:          ./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee dryrun forceKr --lang zh-cn,kr"
                
Parameters:
  --dryrun              干运行模式，只显示将要翻译的记录而不实际执行翻译操作
  --lang <languages>    指定要翻译的目标语言，支持单个语言（如 --lang zh）或多个语言（如 --lang zh,kr）
                        支持的语言: zh（中文繁体）, zh-cn（中文简体）, kr（韩文）
  --forceKr             强制翻译所有记录的韩文版本，包括已有翻译的记录（覆盖现有翻译）

Translation Strategy:
  - 中文繁体和简体：当指定其中一种语言时，优先使用LLM翻译该语言，然后自动通过JTFTAUTO函数转换生成另一种中文
  - 如果数据库中已存在另一种中文，则直接使用JTFTAUTO函数进行转换，避免重复的LLM调用
  - 韩文：使用LLM翻译模型进行翻译，需要从prompts表获取韩文翻译的专用提示词模板

Create date:    2025-07-01
Author:         Luo xiaowei
Run frequency:  On demand
###

debug = DEBUG()
yargs = require 'yargs'
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
i18n = INCLUDE 'lib.i18n'
translatorManagerLib = INCLUDE 'lib.translator/translatorManager'
llmHelper = INCLUDE 'libapp.llmTranslationHelper'

# 数据库集合
I18nCol = COLLECTION 'chome', 'i18n'
PromptsCol = COLLECTION 'chome', 'prompts'

# 配置和服务
config = CONFIG(['azure','i18n','deepseek','deepL','openAI','gemini','claude'])
translatorManager = translatorManagerLib.createTranslatorManager(config)

# 速度监控器
speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 10,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

# 命令行参数解析
yargs
  .option 'lang', {
    type: 'string'
    description: '指定要翻译的目标语言，支持多个语言用逗号分隔 (zh,zh-cn,kr)'
    default: 'zh-cn'
  }
  .help('help')

options = yargs.argv

# 全局变量
dryRun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()
targetLanguages = []
forceKrAll =  AVGS.indexOf('forceKr') >= 0

# 支持的语言列表
SUPPORTED_LANGUAGES = ['zh', 'zh-cn', 'kr']
CHINESE_LANGUAGES = ['zh', 'zh-cn']

# 翻译提示词缓存
koreanPromptCache = null
chinesePromptCache = null

# AI模型优先级配置 - 按照可用性和质量排序
AI_MODEL_PRIORITY = ['openAI', 'claude', 'gemini', 'deepseek', 'grok', 'rm']

###
将单个模版转换为多模版数组，支持新的translateWithPrompt接口
这个函数将传统的单模版对象转换为新的多模版数组格式，
确保向后兼容性的同时支持多AI模型的优先级选择

@param {Object} template - 单个模版对象，包含以下字段：
  - m_cfg.m_nm: AI模型名称 (如 'gpt', 'claude', 'gemini', 'deepseek')
  - tpl.main: 主要提示模板内容
  - tpl.sys: 系统提示词（可选）
  - vars: 变量定义数组（可选）
@return {Array} 多模版数组，每个元素包含：
  - modelType: AI模型类型 ('gpt'|'claude'|'gemini'|'deepseek'|'grok'|'rm')
  - template: 模版对象
  - priority: 优先级数值（越小优先级越高）

@example
# 输入单个GPT模版
template = {
  m_cfg: { m_nm: 'gpt' },
  tpl: { main: 'Translate {text} to {target_language}' }
}

# 输出多模版数组
result = convertToMultiTemplateArray(template)
// [
//   { modelType: 'gpt', template: template, priority: 1 },
//   { modelType: 'claude', template: template, priority: 2 },
//   { modelType: 'gemini', template: template, priority: 3 },
//   ...
// ]
###
convertToMultiTemplateArray = (template) ->
  unless template?.m_cfg?.m_nm
    debug.warn "模板缺少AI模型配置，使用默认模型列表"
    # 返回默认的多模版数组
    return AI_MODEL_PRIORITY.map (modelKey, index) ->
      {
        modelType: modelKey.replace('openAI', 'gpt')  # 转换为模版中的模型名称
        template: template
        priority: index + 1
      }

  modelName = template.m_cfg.m_nm

  # 将模型名称映射到translatorManager的key
  modelMapping = {
    'gpt': 'openAI'
    'openai': 'openAI'
    'claude': 'claude'
    'gemini': 'gemini'
    'deepseek': 'deepseek'
    'grok': 'grok'
    'rm': 'rm'
  }

  translatorKey = modelMapping[modelName.toLowerCase()]

  if translatorKey
    # 创建多模版数组，首选模型优先级最高
    multiTemplates = []

    # 首选模型（优先级1）
    multiTemplates.push {
      modelType: modelName.toLowerCase()
      template: template
      priority: 1
    }

    # 添加其他模型作为fallback
    fallbackPriority = 2
    for model in AI_MODEL_PRIORITY
      if model isnt translatorKey
        fallbackModelType = model.replace('openAI', 'gpt')  # 转换为模版中的模型名称
        multiTemplates.push {
          modelType: fallbackModelType
          template: template
          priority: fallbackPriority
        }
        fallbackPriority++

    return multiTemplates
  else
    debug.warn "未知的AI模型名称: #{modelName}，使用默认模型列表"
    return AI_MODEL_PRIORITY.map (modelKey, index) ->
      {
        modelType: modelKey.replace('openAI', 'gpt')
        template: template
        priority: index + 1
      }

###
从模板配置中获取translatorList（保持向后兼容）
@param {Object} template - 模板对象
@return {Array} translatorList数组
###
getTranslatorListFromTemplate = (template) ->
  unless template?.m_cfg?.m_nm
    debug.warn "模板缺少AI模型配置，使用默认translatorList"
    return ['openAI', 'claude', 'gemini', 'deepseek']

  modelName = template.m_cfg.m_nm

  # 将模型名称映射到translatorManager的key
  modelMapping = {
    'gpt': 'openAI'
    'openai': 'openAI'
    'claude': 'claude'
    'gemini': 'gemini'
    'deepseek': 'deepseek'
    'grok': 'grok'
    'rm': 'rm'
  }

  translatorKey = modelMapping[modelName.toLowerCase()]

  if translatorKey
    # 返回指定模型优先的列表
    priorityList = [translatorKey]
    # 添加其他模型作为fallback
    for model in AI_MODEL_PRIORITY
      if model isnt translatorKey
        priorityList.push model
    return priorityList
  else
    debug.warn "未知的AI模型名称: #{modelName}，使用默认translatorList"
    return AI_MODEL_PRIORITY.slice()

###
选择最佳模板 - 支持多AI模型优先级
@param {Array} templates - 模板数组
@return {Object} 选中的模板
###
selectBestTemplate = (templates) ->
  unless templates?.length > 0
    return null

  # 如果只有一个模板，直接返回
  if templates.length is 1
    return templates[0]

  # 按照AI模型优先级选择模板
  for modelKey in AI_MODEL_PRIORITY
    modelMapping = {
      'openAI': ['gpt', 'openai']
      'claude': ['claude']
      'gemini': ['gemini']
      'deepseek': ['deepseek']
      'grok': ['grok']
      'rm': ['rm']
    }

    modelNames = modelMapping[modelKey] or []

    for template in templates
      if template.m_cfg?.m_nm
        templateModel = template.m_cfg.m_nm.toLowerCase()
        if templateModel in modelNames
          debug.debug "选择模板: #{template._id} (模型: #{template.m_cfg.m_nm})"
          return template

  # 如果没有匹配的模型，返回第一个模板
  debug.debug "未找到匹配的AI模型，使用第一个模板: #{templates[0]._id}"
  return templates[0]

###
解析目标语言参数
@param {String} langParam - 语言参数字符串，如 'zh,kr' 或 'zh-cn'
@return {Array} 解析后的语言数组
###
parseTargetLanguages = (langParam) ->
  unless langParam
    return ['zh-cn']
  
  languages = langParam.split(',').map((lang) -> lang.trim()).filter((lang) -> lang.length > 0)
  validLanguages = []
  
  for lang in languages
    if lang in SUPPORTED_LANGUAGES
      validLanguages.push(lang)
    else
      debug.warn '不支持的语言: ' + lang + '，支持的语言: ' + SUPPORTED_LANGUAGES.join(', ')

  if validLanguages.length is 0
    debug.warn '没有有效的目标语言，使用默认语言: zh-cn'
    return ['zh-cn']
  
  return validLanguages

###
获取智能翻译提示词 - 集成上下文处理
@param {String} text - 要翻译的文本
@param {String} sourceLang - 源语言
@param {String} targetLang - 目标语言
@param {String} context - 上下文信息
@return {Object} {success: boolean, prompt?: string, template?: string, translatorList?: Array, error?: string}
###
getSmartTranslationPrompt = (text, sourceLang, targetLang, context) ->
  # 选择合适的翻译场景
  scenario = llmHelper.selectTranslationScenario(text, context)

  # 查询对应的模板 - 支持多AI模型优先级选择
  query = {
    scenario: scenario
    status: 'active'
    tags: { $in: ['multilingual', 'universal'] }
  }

  templates = await PromptsCol.findToArray query, {
    sort: { ver: -1, _mt: -1 }
  }

  unless templates.length > 0
    # 回退到通用模板
    fallbackQuery = {
      scenario: 'universal_translation'
      status: 'active'
    }

    templates = await PromptsCol.findToArray fallbackQuery, {
      sort: { ver: -1, _mt: -1 }
    }

  # 如果还是没有找到，尝试查找任何活跃的翻译模板
  unless templates.length > 0
    finalFallbackQuery = {
      status: 'active'
      'tpl.main': { $exists: true }
      'm_cfg.m_nm': { $exists: true }
    }

    templates = await PromptsCol.findToArray finalFallbackQuery, {
      sort: { ver: -1, _mt: -1 }
      limit: 10  # 限制数量避免过多结果
    }

  unless templates.length > 0
    return {
      success: false
      error: '未找到任何可用的翻译模板，请检查prompts表中是否有活跃的模板'
    }

  # 选择最佳模板 - 支持多AI模型优先级
  selectedTemplate = selectBestTemplate(templates)

  # 准备变量替换
  variables = {
    text: text
    source_language: llmHelper.LANGUAGE_MAPPING[sourceLang] or sourceLang
    target_language: llmHelper.LANGUAGE_MAPPING[targetLang] or targetLang
    context: context or ''
  }

  # 从模板配置中获取translatorList
  translatorList = getTranslatorListFromTemplate(selectedTemplate)

  return {
    success: true
    template: selectedTemplate  # 返回完整的模版对象
    templateId: selectedTemplate._id
    scenario: scenario
    context: context
    variables: variables  # 返回变量映射
    translatorList: translatorList
    # 保持向后兼容性 - 同时提供处理后的prompt字符串
    prompt: llmHelper.replaceTemplateVariables(selectedTemplate.tpl.main, variables)
  }

###
使用JTFTAUTO函数进行中文繁简转换
@param {String} text - 要转换的文本
@param {String} targetLang - 目标语言 ('zh' 或 'zh-cn')
@return {String} 转换后的文本
###
convertChineseText = (text, targetLang) ->
  unless text
    return text

  try
    return i18n.JTFTAUTO(text, targetLang)
  catch error
    debug.error '中文繁简转换失败:', error
    return text

###
获取中文语言的配对语言
@param {String} lang - 中文语言 ('zh' 或 'zh-cn')
@return {String} 配对的中文语言
###
getChinesePairedLanguage = (lang) ->
  if lang is 'zh'
    return 'zh-cn'
  else if lang is 'zh-cn'
    return 'zh'
  else
    return null

###
检查是否需要处理中文配对语言
@param {Object} record - 数据库记录
@param {String} currentLang - 当前处理的语言
@param {Array} targetLanguages - 目标语言列表
@return {Boolean} 是否需要处理配对语言
###
shouldProcessChinesePair = (record, currentLang, targetLanguages) ->
  unless currentLang in CHINESE_LANGUAGES
    return false

  pairedLang = getChinesePairedLanguage(currentLang)
  unless pairedLang
    return false

  # 检查配对语言是否需要处理（不存在或为空，且不在当前处理列表中）
  return ((not record[pairedLang]) or (record[pairedLang] is '')) and (pairedLang not in targetLanguages)

###
构建查询条件以查找未翻译的记录
@param {Array} languages - 目标语言数组
@param {Boolean} forceKrAll - 是否强制重新翻译所有韩文记录
@return {Object} MongoDB查询条件
###
buildUntranslatedQuery = (languages, forceKrAll) ->
  query = { }
  
  # 构建语言过滤条件
  languageConditions = []
  
  for lang in languages
    if lang is 'kr' and forceKrAll
      # 强制翻译所有韩文记录，不检查是否已存在
      continue
    else
      # 查找该语言字段不存在或为空的记录
      condition = {}
      condition[lang] = { $exists: false }
      languageConditions.push(condition)
      
      # 同时查找该语言字段为空字符串的记录
      emptyCondition = {}
      emptyCondition[lang] = { $in: [null, ''] }
      languageConditions.push(emptyCondition)
  
  if languageConditions.length > 0
    query.$or = languageConditions
  
  return query

# 解析目标语言
targetLanguages = parseTargetLanguages(options.lang)
debug.info "目标语言: #{targetLanguages.join(', ')}"
debug.info "干运行模式: #{dryRun}"
debug.info "强制翻译韩文: #{forceKrAll}"

###
翻译单个记录的指定语言 - 改进版支持上下文处理
@param {Object} record - i18n记录对象
@param {String} targetLang - 目标语言
@return {Object} 翻译结果 {success: boolean, translatedText?: string, error?: string}
###
translateRecord = (record, targetLang) ->
  # 使用orig字段，如果不存在则使用_id
  originalText = record.orig or record._id
  unless originalText
    return {success: false, error: '原文内容为空'}

  # 提取上下文信息和清理文本
  contextInfo = llmHelper.extractContextFromId(originalText)
  cleanText = contextInfo.cleanText
  extractedContext = contextInfo.contextDesc

  # 获取默认上下文（对于短文本自动添加房产上下文）
  defaultContext = llmHelper.getDefaultContext(cleanText, contextInfo.context)
  finalContext = extractedContext or defaultContext

  debug.debug "翻译记录 #{record._id}: 原文='#{originalText}', 清理后='#{cleanText}', 上下文='#{finalContext}'"

  # 中文翻译处理
  if targetLang in CHINESE_LANGUAGES
    # 检查是否已有其他中文版本可以转换
    if (targetLang is 'zh') and record['zh-cn'] and (not record['zh'])
      # 从简体转换为繁体
      translatedText = convertChineseText(record['zh-cn'], 'zh')
      return {success: true, translatedText: translatedText, method: 'convert_from_zhcn'}
    else if (targetLang is 'zh-cn') and record['zh'] and (not record['zh-cn'])
      # 从繁体转换为简体
      translatedText = convertChineseText(record['zh'], 'zh-cn')
      return {success: true, translatedText: translatedText, method: 'convert_from_zh'}
    else
      # 需要LLM翻译中文 - 使用新的智能翻译系统
      # 注意：即使另一个中文字段不存在，也优先使用LLM翻译，然后在主处理逻辑中自动处理配对转换
      try
        smartResult = await getSmartTranslationPrompt(
          cleanText,  # 使用清理后的文本
          'en',       # i18n默认语言为英文
          targetLang,
          finalContext
        )

        unless smartResult.success
          debug.error "智能翻译失败 #{record._id}:", smartResult.error
          return {success: false, error: smartResult.error}

        # 使用新的多模版系统进行翻译
        multiTemplates = convertToMultiTemplateArray(smartResult.template)
        result = await translatorManager.translateWithPrompt(
          cleanText,  # 使用清理后的文本进行翻译
          multiTemplates,  # 传递多模版数组
          targetLang,
          {
            skipI18nFilter: true,  # i18n内容跳过过滤
            variables: smartResult.variables  # 传递变量映射
          }
        )

        if result.success
          return {
            success: true
            translatedText: result.result
            method: 'smart_llm_translate'
            service: result.service
            context: finalContext
            template: smartResult.template
          }
        else
          return {success: false, error: result.error}

      catch error
        debug.error "中文智能翻译异常 #{record._id}:", error
        return {success: false, error: error.message}

  # 韩文翻译处理
  else if targetLang is 'kr'
    try
      # 使用新的智能翻译系统
      smartResult = await getSmartTranslationPrompt(
        cleanText,  # 使用清理后的文本
        'en',       # 假设源语言为英文
        'kr',
        finalContext
      )

      unless smartResult.success
        debug.error "韩文智能翻译失败 #{record._id}:", smartResult.error
        return {success: false, error: smartResult.error}

      # 使用新的多模版系统进行翻译
      multiTemplates = convertToMultiTemplateArray(smartResult.template)
      result = await translatorManager.translateWithPrompt(
        cleanText,  # 使用清理后的文本进行翻译
        multiTemplates,  # 传递多模版数组
        'kr',
        {
          skipI18nFilter: true,  # i18n内容跳过过滤
          variables: smartResult.variables  # 传递变量映射
        }
      )

      if result.success
        return {
          success: true
          translatedText: result.result
          method: 'smart_llm_translate'
          service: result.service
          context: finalContext
          template: smartResult.template
        }
      else
        return {success: false, error: result.error}

    catch error
      debug.error "韩文智能翻译异常 #{record._id}:", error
      return {success: false, error: error.message}

  else
    return {success: false, error: '不支持的目标语言: ' + targetLang}

###
重试翻译操作
@param {Function} translateFn - 翻译函数
@param {Number} maxRetries - 最大重试次数
@param {Number} retryDelay - 重试延迟（毫秒）
@return {Object} 翻译结果
###
retryTranslation = (translateFn, maxRetries = 3, retryDelay = 1000) ->
  for attempt in [1..maxRetries]
    # 执行翻译函数
    try
      result = await translateFn()
    catch error
      if attempt < maxRetries
        debug.warn "翻译异常，第 #{attempt} 次重试: #{error.message}"
        try
          await new Promise((resolve) -> setTimeout(resolve, retryDelay * attempt))
        catch delayError
          debug.error "重试延迟失败:", delayError
        continue
      else
        return {success: false, error: error.message}

    # 检查翻译结果
    if result.success
      return result
    else if attempt < maxRetries
      debug.warn "翻译失败，第 #{attempt} 次重试: #{result.error}"
      try
        await new Promise((resolve) -> setTimeout(resolve, retryDelay * attempt))
      catch delayError
        debug.error "重试延迟失败:", delayError
    else
      return result

  return {success: false, error: '重试次数已用完'}

###
验证翻译结果质量
@param {String} originalText - 原文
@param {String} translatedText - 译文
@param {String} targetLang - 目标语言
@return {Object} 验证结果 {valid: boolean, issues: Array}
###
validateTranslation = (originalText, translatedText, targetLang) ->
  issues = []

  # 基本检查
  unless translatedText
    issues.push '翻译结果为空'
    return {valid: false, issues}

  # 长度检查（译文不应该比原文长太多）
  if translatedText.length > originalText.length * 3
    issues.push '译文长度异常（超过原文3倍）'

  # 检查是否包含明显的错误标识
  errorPatterns = [
    /error/i,
    /failed/i,
    /无法翻译/,
    /translation failed/i,
    /sorry/i
  ]

  for pattern in errorPatterns
    if pattern.test(translatedText)
      issues.push "译文包含错误标识: #{pattern}"

  # 语言特定检查
  if targetLang is 'kr'
    # 韩文应该包含韩文字符
    unless /[\u3131-\u3163\uac00-\ud7a3]/u.test(translatedText)
      issues.push '韩文翻译结果不包含韩文字符'
  else if targetLang in ['zh', 'zh-cn']
    # 中文应该包含中文字符
    unless /[\u4e00-\u9fff]/u.test(translatedText)
      issues.push '中文翻译结果不包含中文字符'

  return {
    valid: issues.length is 0
    issues: issues
  }

###
生成处理报告
@param {Object} stats - 统计数据
###
generateReport = (stats) ->
  report = []
  report.push '=== i18n自动翻译处理报告 ==='
  report.push "处理时间: #{new Date().toISOString()}"
  report.push "目标语言: #{targetLanguages.join(', ')}"
  report.push "处理模式: #{if dryRun then '干运行' else '实际执行'}"
  report.push ''

  report.push '=== 处理统计 ==='
  report.push "总处理记录: #{stats.processed or 0}"
  report.push "成功翻译: #{stats.translated or 0}"
  report.push "跳过记录: #{stats.skipped or 0}"
  report.push "翻译错误: #{stats.translationError or 0}"
  report.push "更新错误: #{stats.updateError or 0}"
  report.push "数据库更新: #{stats.updated or 0}"

  if stats.translated > 0
    totalAttempts = stats.translated + (stats.translationError or 0)
    successRate = ((stats.translated / totalAttempts) * 100).toFixed(2)
    report.push "翻译成功率: #{successRate}%"

  report.push ''
  report.push '=== 语言分布 ==='
  for lang in targetLanguages
    count = stats["#{lang}_translated"] or 0
    report.push "#{lang}: #{count} 条记录"

  if stats.qualityIssues and stats.qualityIssues.length > 0
    report.push ''
    report.push '=== 质量问题 ==='
    for issue in stats.qualityIssues.slice(0, 10)  # 只显示前10个问题
      report.push "- #{issue}"

  report.push ''
  report.push '=== 建议 ==='
  if (stats.translationError or 0) > (stats.translated or 0) * 0.1
    report.push '- 翻译错误率较高，建议检查网络连接和API配置'
  if (stats.updateError or 0) > 0
    report.push '- 存在数据库更新错误，建议检查数据库连接和权限'
  if stats.translated > 0
    report.push '- 建议对翻译结果进行人工抽查以确保质量'

  debug.info report.join('\n')

###
主处理函数
###
main = ->
  # 构建查询条件
  query = buildUntranslatedQuery(targetLanguages, forceKrAll)
  debug.info '查询条件:', JSON.stringify(query)

  # 构建投影，只获取需要的字段
  projection = {
    _id: 1,
    orig: 1
  }

  # 添加目标语言字段到投影中
  for lang in targetLanguages
    projection[lang] = 1

  # 添加中文字段以支持繁简转换
  if 'zh' in targetLanguages or 'zh-cn' in targetLanguages
    projection['zh'] = 1
    projection['zh-cn'] = 1

  # 执行查询
  cur = await I18nCol.find query, {projection: projection}
  cur.maxTimeMS 3600000  # 设置查询超时时间为1小时
  stream = cur.stream()

  # 使用helpers.streaming进行流式处理
  obj =
    verbose: 1,
    stream: stream,
    high: 10,  # 控制并发数
    end: (err) ->
      processTs = (new Date().getTime() - startTs.getTime()) / (1000 * 60)
      if err
        debug.error "处理过程中出错，总处理时间 #{processTs} 分钟。", speedMeter.toString()
        return EXIT 1, err

      # 生成处理报告
      stats = speedMeter.getCounters()
      stats.processTime = processTs
      generateReport(stats)

      debug.info "处理完成，总处理时间 #{processTs} 分钟，#{speedMeter.toString()}"
      EXIT 0
    process: (record, callback) ->
      speedMeter.check { processed: 1 }

      # 准备更新数据
      updateData = {
        noModifyMt: true,
        $set: {}
      }

      translationResults = []
      hasUpdates = false

      # 处理每个目标语言
      for targetLang in targetLanguages
        # 检查是否需要翻译该语言
        shouldTranslate = false

        if targetLang is 'kr' and forceKrAll
          # 强制翻译韩文
          shouldTranslate = true
        else if (not record[targetLang]) or (record[targetLang] is '')
          # 该语言字段不存在或为空
          shouldTranslate = true

        if shouldTranslate
          if dryRun
            debug.info "DryRun: 将翻译记录 #{record._id} 到 #{targetLang}"
            speedMeter.check { dryRun: 1 }
          else
            # 执行翻译（带重试机制）
            try
              translationResult = await retryTranslation ->
                translateRecord(record, targetLang)
            catch error
              speedMeter.check { translationError: 1 }
              debug.error "翻译重试机制失败 #{record._id} -> #{targetLang}:", error
              continue

            translationResults.push({
              language: targetLang,
              result: translationResult
            })

            if translationResult.success
              # 验证翻译质量
              validation = validateTranslation(
                record.orig or record._id,
                translationResult.translatedText,
                targetLang
              )

              if validation.valid
                updateData.$set[targetLang] = translationResult.translatedText
                hasUpdates = true
                speedMeter.check { translated: 1 }
                speedMeter.check { "#{targetLang}_translated": 1 }
                debug.debug "成功翻译 #{record._id} -> #{targetLang}: #{translationResult.translatedText}"

                # 如果是中文翻译且使用了LLM翻译，自动处理配对的中文语言
                if targetLang in CHINESE_LANGUAGES and translationResult.method is 'smart_llm_translate'
                  pairedLang = getChinesePairedLanguage(targetLang)

                  # 检查是否需要处理配对语言
                  if pairedLang and shouldProcessChinesePair(record, targetLang, targetLanguages)
                    try
                      convertedText = convertChineseText(translationResult.translatedText, pairedLang)
                      if convertedText and convertedText isnt translationResult.translatedText
                        updateData.$set[pairedLang] = convertedText
                        speedMeter.check { translated: 1 }
                        speedMeter.check { "#{pairedLang}_translated": 1 }
                        debug.debug "自动转换中文配对 #{record._id} -> #{pairedLang}: #{convertedText}"
                      else
                        debug.warn "中文配对转换结果无效或相同 #{record._id} -> #{pairedLang}"
                    catch convertError
                      debug.warn "中文配对转换失败 #{record._id} -> #{pairedLang}:", convertError
              else
                speedMeter.check { qualityIssue: 1 }
                debug.warn "翻译质量问题 #{record._id} -> #{targetLang}: #{validation.issues.join(', ')}"
                # 仍然保存翻译结果，但记录质量问题
                updateData.$set[targetLang] = translationResult.translatedText
                hasUpdates = true
                speedMeter.check { translated: 1 }
                speedMeter.check { "#{targetLang}_translated": 1 }
            else
              speedMeter.check { translationError: 1 }
              debug.error "翻译失败 #{record._id} -> #{targetLang}: #{translationResult.error}"
        else
          speedMeter.check { skipped: 1 }
          debug.debug "跳过已翻译的记录 #{record._id} -> #{targetLang}"

      # 如果没有更新或干运行模式，不更新数据库
      if dryRun or (not hasUpdates)
        return callback()

      try
        await I18nCol.updateOne { _id: record._id }, updateData
        speedMeter.check { updated: 1 }
        debug.debug "成功更新数据库记录: #{record._id}"
      catch updateError
        speedMeter.check { updateError: 1 }
        debug.error "更新数据库失败 #{record._id}:", updateError

      return callback()

  helpers.streaming obj

# 启动主函数
main()
